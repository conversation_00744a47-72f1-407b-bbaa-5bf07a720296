/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.rocketmq.proxy.common;

public class ContextVariable {
    public static final String REMOTE_ADDRESS = "remote-address";
    public static final String LOCAL_ADDRESS = "local-address";
    public static final String CLIENT_ID = "client-id";
    public static final String CHANNEL = "channel";
    public static final String LANGUAGE = "language";
    public static final String CLIENT_VERSION = "client-version";
    public static final String REMAINING_MS = "remaining-ms";
    public static final String ACTION = "action";
    public static final String PROTOCOL_TYPE = "protocol-type";
    public static final String NAMESPACE = "namespace";
}
