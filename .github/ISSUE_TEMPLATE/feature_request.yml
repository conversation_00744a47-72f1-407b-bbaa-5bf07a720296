#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#


name: Feature Request
title: "[Feature] New feature title"
description: Suggest an idea for this project.
labels: [ "type/new feature" ]
body:
  - type: textarea
    attributes:
      label: Is Your Feature Request Related to a Problem?
      description: Please Describe It.
      placeholder: >
        A clear and concise description of what the problem is.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe the Solution You'd Like
      description: Describe how you solved it.
      placeholder: >
        A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe Alternatives You've Considered
      description: Describe your solution
      placeholder: >
        A clear and concise description of any alternative solutions or features you've considered.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Additional Context
      description: Additional context.
      placeholder: >
        Add any other context about the problem here.
    validations:
      required: false
