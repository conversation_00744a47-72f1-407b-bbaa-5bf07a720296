name: Build and Run Tests by <PERSON><PERSON>
on:
  pull_request:
    types: [opened, reopened, synchronize]
  push:
    branches: [master, develop, bazel]

jobs:
  java_build:
    name: "maven-compile (${{ matrix.os }}, JDK-${{ matrix.jdk }})"
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        # see https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners#supported-runners-and-hardware-resources
        os: [ubuntu-latest, windows-latest, macos-latest]
        jdk: [8]
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up JDK ${{ matrix.jdk }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ matrix.jdk }}
          # See https://github.com/actions/setup-java?tab=readme-ov-file#supported-distributions
          # AdoptOpenJDK got moved to Eclipse Temurin and won't be updated anymore.
          distribution: "corretto"
          cache: "maven"
      - name: Build with <PERSON><PERSON>
        run: mvn -B package --file pom.xml

      - name: Upload Auth JVM crash logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: jvm-crash-logs
          path: /Users/<USER>/work/rocketmq/rocketmq/auth/hs_err_pid*.log
          retention-days: 1

      - name: Upload broker JVM crash logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: jvm-crash-logs
          path: /Users/<USER>/work/rocketmq/rocketmq/broker/hs_err_pid*.log
          retention-days: 1
