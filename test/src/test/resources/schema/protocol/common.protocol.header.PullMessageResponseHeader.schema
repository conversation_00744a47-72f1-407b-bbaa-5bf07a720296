/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


Field maxOffset : private java.lang.Long null
Field minOffset : private java.lang.Long null
Field nextBeginOffset : private java.lang.Long null
Field suggestWhichBrokerId : private java.lang.Long null
Method checkFields() : public throws (void)
Method decode(java.util.HashMap) : public throws (void)
Method encode(io.netty.buffer.ByteBuf) : public throws (void)
Method getMaxOffset() : public throws (java.lang.Long)
Method getMinOffset() : public throws (java.lang.Long)
Method getNextBeginOffset() : public throws (java.lang.Long)
Method getSuggestWhichBrokerId() : public throws (java.lang.Long)
Method setMaxOffset(java.lang.Long) : public throws (void)
Method setMinOffset(java.lang.Long) : public throws (void)
Method setNextBeginOffset(java.lang.Long) : public throws (void)
Method setSuggestWhichBrokerId(java.lang.Long) : public throws (void)
