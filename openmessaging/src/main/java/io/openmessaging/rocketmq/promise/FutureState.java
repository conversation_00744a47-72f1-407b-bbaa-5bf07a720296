/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.openmessaging.rocketmq.promise;

public enum FutureState {
    /**
     * the task is doing
     **/
    DOING(0),
    /**
     * the task is done
     **/
    DONE(1),
    /**
     * ths task is cancelled
     **/
    CANCELLED(2);

    public final int value;

    private FutureState(int value) {
        this.value = value;
    }

    public boolean isCancelledState() {
        return this == CANCELLED;
    }

    public boolean isDoneState() {
        return this == DONE;
    }

    public boolean isDoingState() {
        return this == DOING;
    }
}
