<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at
      http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<FindBugsFilter>
    <Match>
        <Class name="org.apache.rocketmq.remoting.netty.TlsSystemConfig"/>
        <Field name="tlsConfigFile"/>
        <Bug pattern="MS_SHOULD_BE_FINAL"/>
    </Match>
    <Match>
        <Class name="org.apache.rocketmq.remoting.netty.TlsSystemConfig"/>
        <Field name="tlsMode"/>
        <Bug pattern="MS_SHOULD_BE_FINAL"/>
    </Match>
    <Match>
        <Class name="org.apache.rocketmq.store.index.IndexFile"/>
        <Method name="indexKeyHashMethod" />
        <Bug pattern="RV_ABSOLUTE_VALUE_OF_HASHCODE"/>
    </Match>
    <Match>
        <Class name="org.apache.rocketmq.tieredstore.index.TieredIndexFile"/>
        <Method name="indexKeyHashMethod" />
        <Bug pattern="RV_ABSOLUTE_VALUE_OF_HASHCODE"/>
    </Match>
    <Match>
        <Class name="org.apache.rocketmq.broker.transaction.queue.DefaultTransactionalMessageCheckListener"/>
        <Method name="toMessageExtBrokerInner" />
        <Bug pattern="INT_BAD_REM_BY_1"/>
    </Match>
    <Match>
        <Class name="org.apache.rocketmq.tools.command.cluster.ClusterListSubCommand"/>
        <Method name="execute" />
        <Bug pattern="IL_INFINITE_LOOP"/>
    </Match>
</FindBugsFilter>
