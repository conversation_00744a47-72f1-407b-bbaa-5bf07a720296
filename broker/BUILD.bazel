#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
load("//bazel:GenTestRules.bzl", "GenTestRules")

java_library(
    name = "broker",
    srcs = glob(["src/main/java/**/*.java"]),
    visibility = ["//visibility:public"],
    deps = [
        "//auth",
        "//client",
        "//common",
        "//filter",
        "//remoting",
        "//srvutil",
        "//store",
        "//tieredstore",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:ch_qos_logback_logback_classic",
        "@maven//:com_alibaba_fastjson",
        "@maven//:com_alibaba_fastjson2_fastjson2",
        "@maven//:com_github_luben_zstd_jni",
        "@maven//:com_google_guava_guava",
        "@maven//:com_googlecode_concurrentlinkedhashmap_concurrentlinkedhashmap_lru",
        "@maven//:commons_cli_commons_cli",
        "@maven//:commons_collections_commons_collections",
        "@maven//:commons_io_commons_io",
        "@maven//:commons_validator_commons_validator",
        "@maven//:io_netty_netty_all",
        "@maven//:io_openmessaging_storage_dledger",
        "@maven//:io_opentelemetry_opentelemetry_api",
        "@maven//:io_opentelemetry_opentelemetry_context",
        "@maven//:io_opentelemetry_opentelemetry_exporter_otlp",
        "@maven//:io_opentelemetry_opentelemetry_exporter_prometheus",
        "@maven//:io_opentelemetry_opentelemetry_exporter_logging",
        "@maven//:io_opentelemetry_opentelemetry_exporter_logging_otlp",
        "@maven//:io_opentelemetry_opentelemetry_sdk",
        "@maven//:io_opentelemetry_opentelemetry_sdk_common",
        "@maven//:io_opentelemetry_opentelemetry_sdk_metrics",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:commons_codec_commons_codec",
        "@maven//:org_lz4_lz4_java",
        "@maven//:io_github_aliyunmq_rocketmq_slf4j_api",
        "@maven//:io_github_aliyunmq_rocketmq_logback_classic",
        "@maven//:org_slf4j_jul_to_slf4j",
        "@maven//:io_github_aliyunmq_rocketmq_shaded_slf4j_api_bridge",
        "@maven//:org_apache_rocketmq_rocketmq_rocksdb",
        "@maven//:net_java_dev_jna_jna",
    ],
)

java_library(
    name = "tests",
    srcs = glob(["src/test/java/**/*.java"]),
    resources = [
        "src/test/resources/META-INF/service/org.apache.rocketmq.broker.transaction.AbstractTransactionalMessageCheckListener",
        "src/test/resources/META-INF/service/org.apache.rocketmq.broker.transaction.TransactionalMessageService",
        "src/test/resources/rmq.logback-test.xml",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":broker",
        "//:test_deps",
        "//auth",
        "//client",
        "//common",
        "//filter",
        "//remoting",
        "//store",
        "//tieredstore",
        "@maven//:com_alibaba_fastjson",
        "@maven//:com_alibaba_fastjson2_fastjson2",
        "@maven//:org_slf4j_slf4j_api",
        "@maven//:com_google_guava_guava",
        "@maven//:io_netty_netty_all",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:commons_codec_commons_codec",
        "@maven//:commons_io_commons_io",
        "@maven//:io_github_aliyunmq_rocketmq_slf4j_api",
        "@maven//:org_powermock_powermock_core",
        "@maven//:io_opentelemetry_opentelemetry_api",
        "@maven//:com_googlecode_concurrentlinkedhashmap_concurrentlinkedhashmap_lru",
        "@maven//:org_apache_rocketmq_rocketmq_rocksdb",
        "@maven//:commons_collections_commons_collections",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
    ],
)

GenTestRules(
    name = "GeneratedTestRules",
    test_files = glob(["src/test/java/**/*Test.java"]),
    exclude_tests = [
            # These tests are extremely slow and flaky, exclude them before they are properly fixed.
            "src/test/java/org/apache/rocketmq/broker/controller/ReplicasManagerRegisterTest",
            "src/test/java/org/apache/rocketmq/broker/BrokerOuterAPITest",
        ],
    deps = [
        ":tests",
    ],
)
